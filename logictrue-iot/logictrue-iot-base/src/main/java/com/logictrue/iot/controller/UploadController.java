package com.logictrue.iot.controller;

import com.logictrue.iot.service.IFileAttachInfoService;
import com.logictrue.iot.service.ISysFileService;
import com.logictrue.iot.service.UploadService;
import com.logictrue.iot.service.IDeviceService;
import com.logictrue.iot.service.IDeviceTemplateBindingService;
import com.logictrue.iot.service.IExcelTemplateService;
import com.logictrue.iot.service.IDeviceDetectionParseService;
import com.logictrue.common.core.domain.R;
import com.logictrue.common.core.text.UUID;
import com.logictrue.common.core.utils.StringUtils;
import com.logictrue.common.core.utils.file.FileUtils;
import com.logictrue.common.core.utils.DateUtils;
import com.logictrue.iot.entity.FileAttachInfo;
import com.logictrue.iot.entity.Device;
import com.logictrue.iot.entity.ExcelTemplate;
import com.logictrue.iot.entity.DeviceDetectionData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@RestController
public class UploadController {

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private IFileAttachInfoService iFileAttachInfoService;

    @Autowired
    private UploadService uploadService;
    /**
     * 文件上传请求
     */
    @RequestMapping("/upload")
    @ResponseBody
    public R<FileAttachInfo> upload(@RequestParam("file") MultipartFile file, @RequestParam(value = "fileId", required = false) String fileId) {
        try {
            // 上传并返回访问地址
            String url = sysFileService.uploadFile(file, Boolean.FALSE);


            Double fileSize = file.getSize() / 1024D / 1024D;

            // 添加附件数据表
            FileAttachInfo build = FileAttachInfo.builder().fileId(StringUtils.isNotEmpty(fileId) ? fileId : UUID.randomUUID().toString().replaceAll("-", "")).uid(UUID.randomUUID().toString().replaceAll("-", "")).fileName(file.getOriginalFilename()).pathName(FileUtils.getRelativeUrl(url)).fileType(FileUtils.getFileType(url)).fileSize(fileSize).build();
            iFileAttachInfoService.save(build);

            ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attr.getRequest();
            String remoteAddr = request.getRemoteAddr();

            //
            uploadService.saveInfo(build, remoteAddr);

            return R.ok(build);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }
}
